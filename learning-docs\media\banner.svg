<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="200" fill="#2C2C32" />
  
  <!-- Book Icon -->
  <g transform="translate(50, 50) scale(2)">
    <path d="M8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z" fill="#007ACC" />
  </g>
  
  <!-- Text -->
  <text x="150" y="100" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#FFFFFF">Learning Docs</text>
  <text x="150" y="140" font-family="Arial, sans-serif" font-size="24" fill="#CCCCCC">AI-Powered Documentation for Code Changes</text>
  
  <!-- Code Symbols -->
  <g transform="translate(650, 100)">
    <text font-family="monospace" font-size="60" fill="#007ACC">{}</text>
  </g>
  <g transform="translate(700, 100)">
    <text font-family="monospace" font-size="60" fill="#007ACC">()</text>
  </g>
  <g transform="translate(750, 100)">
    <text font-family="monospace" font-size="60" fill="#007ACC">;</text>
  </g>
</svg>
