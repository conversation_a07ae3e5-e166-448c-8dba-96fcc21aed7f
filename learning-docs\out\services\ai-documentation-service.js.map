{"version": 3, "file": "ai-documentation-service.js", "sourceRoot": "", "sources": ["../../src/services/ai-documentation-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAA6B;AAC7B,6CAA+B;AAC/B,2CAA6B;AAM7B;;GAEG;AACH,MAAa,sBAAsB;IAEV;IACA;IAFrB,YACqB,aAA6B,EAC7B,aAA6B;QAD7B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,kBAAa,GAAb,aAAa,CAAgB;IAC/C,CAAC;IAEJ;;;;;OAKG;IACI,KAAK,CAAC,qBAAqB,CAC9B,MAAiC;QAEjC,IAAI,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oCAAoC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE/E,uCAAuC;YACvC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,2CAA2C;YAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAS,cAAc,CAAC,CAAC;YAC3E,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,8BAA8B;YAC9B,MAAM,OAAO,GAAG;gBACZ,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;gBAC7C,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;gBAC3C,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC7B,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,IAAI,EAAE;gBACnD,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;aAC9C,CAAC;YAEF,oCAAoC;YACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAE5E,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mCAAmC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC9E,OAAO,UAAU,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,4CAA4C,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACvF,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;YACxE,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,sBAAsB,CAAC,GAAW,EAAE,OAAY;QAC1D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,yCAAyC;YACzC,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YAEtD,8BAA8B;YAC9B,MAAM,cAAc,GAAG;gBACnB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,kBAAkB;iBACrC;aACJ,CAAC;YAEF,qBAAqB;YACrB,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE;gBACpD,IAAI,IAAI,GAAG,EAAE,CAAC;gBAEd,4BAA4B;gBAC5B,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrB,IAAI,IAAI,KAAK,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,0CAA0C;gBAC1C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACf,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;wBAClE,IAAI,CAAC;4BACD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAe,CAAC;4BAClD,OAAO,CAAC,UAAU,CAAC,CAAC;wBACxB,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;4BACvE,OAAO,CAAC,IAAI,CAAC,CAAC;wBAClB,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;wBAC9E,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,wBAAwB;YACxB,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACtB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;gBACzE,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,mBAAmB;YACnB,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACnC,GAAG,CAAC,GAAG,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,MAAiC;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACT,OAAO,GAAG,iBAAiB,OAAO,UAAU,QAAQ,EAAE,CAAC;gBACvD,WAAW,GAAG,2CAA2C,QAAQ,mBAAmB,CAAC;gBACrF,MAAM;YACV,KAAK,QAAQ;gBACT,OAAO,GAAG,YAAY,QAAQ,EAAE,CAAC;gBACjC,WAAW,GAAG,yCAAyC,QAAQ,GAAG,CAAC;gBACnE,MAAM;YACV,KAAK,QAAQ;gBACT,OAAO,GAAG,WAAW,QAAQ,EAAE,CAAC;gBAChC,WAAW,GAAG,gCAAgC,QAAQ,qBAAqB,CAAC;gBAC5E,MAAM;QACd,CAAC;QAED,yDAAyD;QACzD,IAAI,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpE,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;YACrF,MAAM,iBAAiB,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC;YAC1F,MAAM,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC;YAE3F,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,WAAW,IAAI,aAAa,eAAe,CAAC,MAAM,gBAAgB,CAAC;gBACnE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACxB,WAAW,IAAI,OAAO,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3D,CAAC,CAAC,CAAC;YACP,CAAC;YAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,WAAW,IAAI,eAAe,iBAAiB,CAAC,MAAM,YAAY,CAAC;gBACnE,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAC1B,WAAW,IAAI,OAAO,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3D,CAAC,CAAC,CAAC;YACP,CAAC;YAED,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,WAAW,IAAI,gBAAgB,kBAAkB,CAAC,MAAM,YAAY,CAAC;gBACrE,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAC3B,WAAW,IAAI,OAAO,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3D,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,OAAO;YACH,OAAO;YACP,WAAW;YACX,MAAM,EAAE,yDAAyD;YACjE,WAAW,EAAE;gBACT,sFAAsF;aACzF;YACD,iBAAiB,EAAE;gBACf;oBACI,KAAK,EAAE,gCAAgC;oBACvC,GAAG,EAAE,mCAAmC;oBACxC,WAAW,EAAE,+CAA+C;iBAC/D;aACJ;SACJ,CAAC;IACN,CAAC;CACJ;AA1LD,wDA0LC"}