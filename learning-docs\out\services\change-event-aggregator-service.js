"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeEventAggregatorService = void 0;
const vscode = __importStar(require("vscode"));
const crypto = __importStar(require("crypto"));
/**
 * Implementation of the change event aggregator service.
 */
class ChangeEventAggregatorService {
    loggerService;
    workspaceService;
    _onDidStableChange = new vscode.EventEmitter();
    onDidStableChange = this._onDidStableChange.event;
    disposables = [];
    recentSaveEvents = new Map(); // filePath -> timestamp
    debounceTimeouts = new Map(); // filePath -> timeout
    constructor(loggerService, workspaceService) {
        this.loggerService = loggerService;
        this.workspaceService = workspaceService;
    }
    /**
     * Start listening for changes.
     */
    startListening() {
        this.loggerService.info('Starting to listen for file changes');
        // Listen for save events (primary for modifications)
        this.disposables.push(vscode.workspace.onDidSaveTextDocument(this.handleSaveEvent.bind(this)));
        // Create a file system watcher for all files
        const watcher = vscode.workspace.createFileSystemWatcher('**/*');
        // Handle file creation
        this.disposables.push(watcher.onDidCreate(this.handleCreateEvent.bind(this)));
        // Handle file deletion
        this.disposables.push(watcher.onDidDelete(this.handleDeleteEvent.bind(this)));
        // Handle external file changes
        this.disposables.push(watcher.onDidChange(this.handleExternalChangeEvent.bind(this)));
        // Add the watcher itself to disposables
        this.disposables.push(watcher);
    }
    /**
     * Stop listening for changes.
     */
    stopListening() {
        this.loggerService.info('Stopping file change listeners');
        // Clear all debounce timeouts
        for (const timeout of this.debounceTimeouts.values()) {
            clearTimeout(timeout);
        }
        this.debounceTimeouts.clear();
        // Dispose all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
    /**
     * Handle a save event.
     * @param document The document that was saved.
     */
    handleSaveEvent(document) {
        const filePath = document.uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(document.uri);
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        this.loggerService.info(`File saved: ${relativePath}`);
        // Record this save event to prevent duplicate processing from external change events
        this.recentSaveEvents.set(filePath, Date.now());
        // Create a change descriptor
        const change = {
            id: this.generateId(filePath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'modify',
            currentContent: document.getText()
        };
        // Emit the change event
        this._onDidStableChange.fire(change);
    }
    /**
     * Handle a file creation event.
     * @param uri The URI of the created file.
     */
    async handleCreateEvent(uri) {
        const filePath = uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(uri);
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        this.loggerService.info(`File created: ${relativePath}`);
        try {
            // Read the content of the new file
            const document = await vscode.workspace.openTextDocument(uri);
            const content = document.getText();
            // Create a change descriptor
            const change = {
                id: this.generateId(filePath),
                filePath: relativePath,
                timestamp: new Date().toISOString(),
                eventType: 'create',
                currentContent: content
            };
            // Emit the change event
            this._onDidStableChange.fire(change);
        }
        catch (error) {
            this.loggerService.error(`Failed to process file creation: ${error}`);
        }
    }
    /**
     * Handle a file deletion event.
     * @param uri The URI of the deleted file.
     */
    handleDeleteEvent(uri) {
        const relativePath = this.workspaceService.getRelativePath(uri);
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        this.loggerService.info(`File deleted: ${relativePath}`);
        // Create a change descriptor
        const change = {
            id: this.generateId(uri.fsPath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'delete'
        };
        // Emit the change event
        this._onDidStableChange.fire(change);
    }
    /**
     * Handle an external file change event.
     * @param uri The URI of the changed file.
     */
    handleExternalChangeEvent(uri) {
        const filePath = uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(uri);
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        // Check if we already processed a save event for this file recently
        const lastSaveTime = this.recentSaveEvents.get(filePath);
        if (lastSaveTime && Date.now() - lastSaveTime < 2000) {
            this.loggerService.info(`Ignoring external change for recently saved file: ${relativePath}`);
            return;
        }
        // Clear any existing debounce timeout for this file
        if (this.debounceTimeouts.has(filePath)) {
            clearTimeout(this.debounceTimeouts.get(filePath));
        }
        // Debounce the external change event (500ms)
        this.debounceTimeouts.set(filePath, setTimeout(async () => {
            this.loggerService.info(`External file change: ${relativePath}`);
            try {
                // Read the content of the changed file
                const document = await vscode.workspace.openTextDocument(uri);
                const content = document.getText();
                // Create a change descriptor
                const change = {
                    id: this.generateId(filePath),
                    filePath: relativePath,
                    timestamp: new Date().toISOString(),
                    eventType: 'modify',
                    currentContent: content
                };
                // Emit the change event
                this._onDidStableChange.fire(change);
            }
            catch (error) {
                this.loggerService.error(`Failed to process external file change: ${error}`);
            }
        }, 500));
    }
    /**
     * Generate a unique ID for a change.
     * @param filePath The path to the file.
     * @returns A unique ID.
     */
    generateId(filePath) {
        const timestamp = Date.now().toString();
        const hash = crypto.createHash('md5').update(`${filePath}:${timestamp}`).digest('hex');
        return hash;
    }
    /**
     * Dispose of resources.
     */
    dispose() {
        this.stopListening();
        this._onDidStableChange.dispose();
    }
}
exports.ChangeEventAggregatorService = ChangeEventAggregatorService;
//# sourceMappingURL=change-event-aggregator-service.js.map