{"version": 3, "file": "change-event-aggregator-service.js", "sourceRoot": "", "sources": ["../../src/services/change-event-aggregator-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,+CAAiC;AAMjC;;GAEG;AACH,MAAa,4BAA4B;IAShB;IACA;IATJ,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAoB,CAAC;IAClE,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAE1D,WAAW,GAAwB,EAAE,CAAC;IACtC,gBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAC,CAAC,wBAAwB;IACtE,gBAAgB,GAAG,IAAI,GAAG,EAA0B,CAAC,CAAC,sBAAsB;IAEpF,YACqB,aAA6B,EAC7B,gBAAmC;QADnC,kBAAa,GAAb,aAAa,CAAgB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAAmB;IACrD,CAAC;IAEJ;;OAEG;IACI,cAAc;QACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAE/D,qDAAqD;QACrD,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC1E,CAAC;QAEF,6CAA6C;QAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAEjE,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACzD,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACzD,CAAC;QAEF,+BAA+B;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACjE,CAAC;QAEF,wCAAwC;QACxC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAE1D,8BAA8B;QAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,0BAA0B;QAC1B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,QAA6B;QACjD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,YAAY,EAAE,CAAC,CAAC;QAEvD,qFAAqF;QACrF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAEhD,6BAA6B;QAC7B,MAAM,MAAM,GAAqB;YAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC7B,QAAQ,EAAE,YAAY;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,QAAQ;YACnB,cAAc,EAAE,QAAQ,CAAC,OAAO,EAAE;SACrC,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,iBAAiB,CAAC,GAAe;QAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAEhE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,YAAY,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC;YACD,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEnC,6BAA6B;YAC7B,MAAM,MAAM,GAAqB;gBAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,OAAO;aAC1B,CAAC;YAEF,wBAAwB;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,iBAAiB,CAAC,GAAe;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAEhE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,YAAY,EAAE,CAAC,CAAC;QAEzD,6BAA6B;QAC7B,MAAM,MAAM,GAAqB;YAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;YAC/B,QAAQ,EAAE,YAAY;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,QAAQ;SACtB,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACK,yBAAyB,CAAC,GAAe;QAC7C,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAEhE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,oEAAoE;QACpE,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,GAAG,IAAI,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qDAAqD,YAAY,EAAE,CAAC,CAAC;YAC7F,OAAO;QACX,CAAC;QAED,oDAAoD;QACpD,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;YACtD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;YAEjE,IAAI,CAAC;gBACD,uCAAuC;gBACvC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC9D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAEnC,6BAA6B;gBAC7B,MAAM,MAAM,GAAqB;oBAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;oBAC7B,QAAQ,EAAE,YAAY;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,OAAO;iBAC1B,CAAC;gBAEF,wBAAwB;gBACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;YACjF,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,UAAU,CAAC,QAAgB;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;IACtC,CAAC;CACJ;AA/ND,oEA+NC"}