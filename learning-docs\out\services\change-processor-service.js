"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeProcessorService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
/**
 * Implementation of the change processor service.
 */
class ChangeProcessorService {
    loggerService;
    changeEventAggregator;
    storageService;
    fileHistoryService;
    diffService;
    semanticAnalyzerService;
    contextAnalyzerService;
    aiDocumentationService;
    configService;
    disposables = [];
    constructor(loggerService, changeEventAggregator, storageService, fileHistoryService, diffService, semanticAnalyzerService, contextAnalyzerService, aiDocumentationService, configService) {
        this.loggerService = loggerService;
        this.changeEventAggregator = changeEventAggregator;
        this.storageService = storageService;
        this.fileHistoryService = fileHistoryService;
        this.diffService = diffService;
        this.semanticAnalyzerService = semanticAnalyzerService;
        this.contextAnalyzerService = contextAnalyzerService;
        this.aiDocumentationService = aiDocumentationService;
        this.configService = configService;
    }
    /**
     * Start processing changes.
     */
    start() {
        this.loggerService.info('Starting change processor');
        // Start the change event aggregator
        this.changeEventAggregator.startListening();
        // Subscribe to stable change events
        this.disposables.push(this.changeEventAggregator.onDidStableChange(this.processChange.bind(this)));
    }
    /**
     * Stop processing changes.
     */
    stop() {
        this.loggerService.info('Stopping change processor');
        // Stop the change event aggregator
        this.changeEventAggregator.stopListening();
        // Dispose all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
    /**
     * Process a change event.
     * @param change The change to process.
     */
    async processChange(change) {
        this.loggerService.info(`Processing change: ${change.eventType} - ${change.filePath}`);
        try {
            // Create a documentation record from the change
            const record = {
                ...change
            };
            // For 'modify' events, get the previous content
            if (change.eventType === 'modify' && this.fileHistoryService) {
                record.previousContent = await this.fileHistoryService.getPreviousVersion(change.filePath);
            }
            // Generate diff if we have both previous and current content
            if (this.diffService &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                const previousContent = record.previousContent || '';
                const currentContent = change.currentContent;
                // Generate unified diff
                record.rawDiff = await this.diffService.generateUnifiedDiff(previousContent, currentContent, change.filePath, change.filePath);
            }
            // Perform semantic analysis if enabled
            this.loggerService.info(`🔍 CHECKING SEMANTIC ANALYSIS CONDITIONS for ${change.filePath}`);
            this.loggerService.info(`  - Has semantic analyzer service: ${!!this.semanticAnalyzerService}`);
            // DEBUG: Get the value from configService and log it in detail
            const isSemanticGloballyEnabled = this.configService?.isFeatureEnabled('semanticAnalysis');
            this.loggerService.info(`[DEBUG_CONFIG_USAGE] 🎯 Value from configService.isFeatureEnabled('semanticAnalysis') in ChangeProcessorService: ${isSemanticGloballyEnabled} (Type: ${typeof isSemanticGloballyEnabled})`);
            this.loggerService.info(`  - Semantic analysis enabled: ${isSemanticGloballyEnabled}`);
            this.loggerService.info(`  - Event type: ${change.eventType}`);
            this.loggerService.info(`  - Has current content: ${!!change.currentContent}`);
            if (this.semanticAnalyzerService &&
                this.configService?.isFeatureEnabled('semanticAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                this.loggerService.info(`✅ STARTING SEMANTIC ANALYSIS for ${change.filePath}`);
                try {
                    // Determine language ID from file extension
                    const languageId = this.getLanguageIdFromFilePath(change.filePath);
                    this.loggerService.info(`📝 Detected language: ${languageId}`);
                    // Create URI for the file
                    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;
                    const fileUri = workspaceRoot ?
                        vscode.Uri.joinPath(workspaceRoot, change.filePath) :
                        vscode.Uri.file(change.filePath);
                    this.loggerService.info(`📂 File URI: ${fileUri.toString()}`);
                    const semanticAnalysis = await this.semanticAnalyzerService.getSemanticAnalysis(record.previousContent, change.currentContent, fileUri, languageId);
                    if (semanticAnalysis) {
                        record.semanticPrimitives = semanticAnalysis.primitives;
                        this.loggerService.info(`✅ SEMANTIC ANALYSIS SUCCESS for ${change.filePath}: ${semanticAnalysis.primitives.length} primitives found`);
                        this.loggerService.info(`📊 Primitives stored in record: ${record.semanticPrimitives?.length || 0}`);
                    }
                    else {
                        this.loggerService.warn(`⚠️ SEMANTIC ANALYSIS RETURNED NULL for ${change.filePath}`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`💥 SEMANTIC ANALYSIS FAILED for ${change.filePath}: ${error}`);
                    this.loggerService.error(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace'}`);
                }
            }
            else {
                this.loggerService.info(`⏭️ SKIPPING SEMANTIC ANALYSIS for ${change.filePath} - conditions not met`);
            }
            // Perform contextual analysis if enabled
            if (this.contextAnalyzerService &&
                this.configService?.isFeatureEnabled('contextualAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline')) {
                try {
                    const contextualInfo = await this.contextAnalyzerService.getContextualInfo(change.filePath, record.semanticPrimitives);
                    if (contextualInfo) {
                        record.contextualInfo = contextualInfo;
                        this.loggerService.info(`Contextual analysis completed for ${change.filePath}`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`Contextual analysis failed: ${error}`);
                }
            }
            // Generate AI documentation if enabled
            if (this.aiDocumentationService &&
                this.configService?.isFeatureEnabled('aiDocumentation') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline')) {
                try {
                    // We need to save the record first to ensure it has all the data from previous steps
                    const tempRecord = { ...record };
                    const aiAnalysis = await this.aiDocumentationService.generateDocumentation(tempRecord);
                    if (aiAnalysis) {
                        record.aiAnalysis = aiAnalysis;
                        this.loggerService.info(`AI documentation generated for ${change.filePath}`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`AI documentation generation failed: ${error}`);
                }
            }
            // Save the record
            await this.storageService.saveDocumentationRecord(record);
            // Update the file history cache with the current content
            if (this.fileHistoryService &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                await this.fileHistoryService.updateCache(change.filePath, change.currentContent);
            }
            this.loggerService.info(`Successfully processed change: ${change.id}`);
        }
        catch (error) {
            this.loggerService.error(`Failed to process change: ${error}`);
        }
    }
    /**
     * Get the language ID from a file path.
     * @param filePath The file path.
     * @returns The language ID.
     */
    getLanguageIdFromFilePath(filePath) {
        const extension = path.extname(filePath).toLowerCase();
        const extensionToLanguageMap = {
            '.ts': 'typescript',
            '.js': 'javascript',
            '.tsx': 'typescriptreact',
            '.jsx': 'javascriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.md': 'markdown',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp'
        };
        return extensionToLanguageMap[extension] || 'plaintext';
    }
    /**
     * Dispose of resources.
     */
    dispose() {
        this.stop();
    }
}
exports.ChangeProcessorService = ChangeProcessorService;
//# sourceMappingURL=change-processor-service.js.map