"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Implementation of the configuration service.
 */
class ConfigService {
    loggerService;
    configSection = 'learningDocs';
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    /**
     * Get a configuration setting by key.
     * @param key The configuration key.
     * @returns The configuration value, or undefined if not found.
     */
    getSetting(key) {
        this.loggerService.info(`[DEBUG_CONFIG] 🔍 Attempting to read config: Section='${this.configSection}', Key='${key}'`);
        const config = vscode.workspace.getConfiguration(this.configSection);
        // Debug: Check if the configuration section exists at all
        const allKeys = Object.keys(config);
        this.loggerService.info(`[DEBUG_CONFIG] 📋 All available keys in '${this.configSection}' section:`, allKeys);
        // Debug: Try to get the value with inspect() to see defaults vs user values
        const inspectResult = config.inspect(key);
        this.loggerService.info(`[DEBUG_CONFIG] 🔍 Inspect result for '${key}':`, JSON.stringify(inspectResult, null, 2));
        const rawValue = config.get(key);
        this.loggerService.info(`[DEBUG_CONFIG] 📄 Raw value from getConfiguration('${this.configSection}').get('${key}'):`, rawValue, `(Type: ${typeof rawValue})`);
        return rawValue;
    }
    /**
     * Check if a feature is enabled.
     * @param featureKey The feature key to check.
     * @returns True if the feature is enabled, false otherwise.
     */
    isFeatureEnabled(featureKey) {
        const fullKey = `enable.${featureKey}`;
        this.loggerService.info(`[DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='${featureKey}' -> fullKey='${fullKey}'`);
        const rawValue = this.getSetting(fullKey);
        const result = rawValue ?? false;
        this.loggerService.info(`[DEBUG_CONFIG] ✅ isFeatureEnabled result for '${featureKey}': ${result} (rawValue was: ${rawValue})`);
        return result;
    }
}
exports.ConfigService = ConfigService;
//# sourceMappingURL=config-service.js.map