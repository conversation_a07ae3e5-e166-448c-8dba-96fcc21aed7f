{"version": 3, "file": "context-analyzer-service.js", "sourceRoot": "", "sources": ["../../src/services/context-analyzer-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AACzB,6DAA+C;AAC/C,+BAAiC;AAOjC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEnD;;GAEG;AACH,MAAa,sBAAsB;IAEV;IACA;IACA;IAHrB,YACqB,aAA6B,EAC7B,aAA6B,EAC7B,gBAAmC;QAFnC,kBAAa,GAAb,aAAa,CAAgB;QAC7B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAAmB;IACrD,CAAC;IAEJ;;;;;;OAMG;IACI,KAAK,CAAC,iBAAiB,CAC1B,QAAgB,EAChB,kBAAwC;QAExC,IAAI,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;YAEpE,6BAA6B;YAC7B,MAAM,cAAc,GAAmB,EAAE,CAAC;YAE1C,oBAAoB;YACpB,cAAc,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAExF,iBAAiB;YACjB,cAAc,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAEpF,sBAAsB;YACtB,cAAc,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAExE,2BAA2B;YAC3B,cAAc,CAAC,mBAAmB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAElF,qBAAqB;YACrB,cAAc,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAErE,oBAAoB;YACpB,cAAc,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEpE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;YAC1E,OAAO,cAAc,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,gBAAgB,CAC1B,QAAgB,EAChB,kBAAwC;QAExC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QAEvC,IAAI,CAAC;YACD,iEAAiE;YACjE,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,oCAAoC;gBACpC,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBAEvE,wCAAwC;gBACxC,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;oBACrC,0DAA0D;oBAC1D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAClB,SAAS;oBACb,CAAC;oBAED,+DAA+D;oBAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;oBAC/D,IAAI,aAAa,EAAE,CAAC;wBAChB,IAAI,CAAC;4BACD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE;gCACpE,GAAG,EAAE,aAAa,CAAC,MAAM;6BAC5B,CAAC,CAAC;4BAEH,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;4BACzE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACb,4DAA4D;4BAC5D,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;4BAE/E,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gCACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gCAChE,IAAI,YAAY,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;oCAC5C,IAAI,CAAC;wCACD,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;wCAChE,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;4CACzB,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;wCACnC,CAAC;oCACL,CAAC;oCAAC,OAAO,KAAK,EAAE,CAAC;wCACb,8BAA8B;oCAClC,CAAC;gCACL,CAAC;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,gCAAgC;YAChC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAE/D,IAAI,aAAa,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,YAAY,GAAG,EAAE,oBAAoB,CAAC,CAAC;gBAE/F,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;oBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAChE,IAAI,YAAY,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;wBAC5C,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACnC,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc,CACxB,QAAgB,EAChB,kBAAwC;QAExC,MAAM,UAAU,GAAiC,EAAE,CAAC;QAEpD,IAAI,CAAC;YACD,8DAA8D;YAC9D,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,oCAAoC;gBACpC,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBAEvE,wCAAwC;gBACxC,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;oBACrC,0DAA0D;oBAC1D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAClB,SAAS;oBACb,CAAC;oBAED,+DAA+D;oBAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;oBAC/D,IAAI,aAAa,EAAE,CAAC;wBAChB,IAAI,CAAC;4BACD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gCAClD,GAAG,EAAE,aAAa,CAAC,MAAM;6BAC5B,CAAC,CAAC;4BAEH,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACxC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gCACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;gCAC9C,IAAI,KAAK,EAAE,CAAC;oCACR,MAAM,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;oCAErD,uBAAuB;oCACvB,IAAI,aAAa,KAAK,QAAQ,EAAE,CAAC;wCAC7B,SAAS;oCACb,CAAC;oCAED,UAAU,CAAC,IAAI,CAAC;wCACZ,QAAQ,EAAE,aAAa;wCACvB,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC;wCACpC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;qCAC1B,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACb,4DAA4D;4BAC5D,yDAAyD;wBAC7D,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,UAAU,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC7C,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,IAAI,CAAC;YACD,wBAAwB;YACxB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACd,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE9D,0CAA0C;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAEvC,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;gBACrF,wCAAwC;gBACxC,MAAM,WAAW,GAAG,uEAAuE,CAAC;gBAC5F,IAAI,KAAK,CAAC;gBAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBAClD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC;gBAED,oCAAoC;gBACpC,MAAM,YAAY,GAAG,uCAAuC,CAAC;gBAE7D,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACnD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;gBAC3B,yBAAyB;gBACzB,MAAM,WAAW,GAAG,iDAAiD,CAAC;gBACtE,IAAI,KAAK,CAAC;gBAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBAClD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC;YACL,CAAC;YACD,uDAAuD;YAEvD,OAAO,cAAc,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACpE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QAClD,MAAM,mBAAmB,GAAa,EAAE,CAAC;QAEzC,IAAI,CAAC;YACD,yBAAyB;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACd,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAExE,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;gBAErF,uBAAuB;gBACvB,MAAM,eAAe,GAAG;oBACpB,GAAG,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC;oBACnC,GAAG,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC;iBACzC,CAAC;gBAEF,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC3D,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAE9D,gDAAgD;gBAChD,KAAK,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;oBAChC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBACxB,mBAAmB,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC/D,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,mBAAmB,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;YACzE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC3C,MAAM,aAAa,GAAoC,EAAE,CAAC;QAE1D,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACd,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC;gBACD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,CAC7B,KAAK,EACL,CAAC,KAAK,EAAE,+BAA+B,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,CAAC,EACtE,EAAE,GAAG,EAAE,aAAa,CAAC,MAAM,EAAE,CAChC,CAAC;gBAEF,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACxC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACvB,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAE5D,aAAa,CAAC,IAAI,CAAC;wBACf,UAAU;wBACV,MAAM;wBACN,IAAI;wBACJ,OAAO;qBACV,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,0EAA0E;gBAC1E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,OAAO,aAAa,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YACnE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC3C,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,CAAC;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAE/D,IAAI,aAAa,EAAE,CAAC;gBAChB,yCAAyC;gBACzC,MAAM,YAAY,GAAG;oBACjB,MAAM,YAAY,SAAS;oBAC3B,MAAM,YAAY,SAAS;oBAC3B,WAAW,YAAY,IAAI;oBAC3B,YAAY,YAAY,IAAI;oBAC5B,WAAW,YAAY,IAAI;oBAC3B,gBAAgB,YAAY,IAAI;iBACnC,CAAC;gBAEF,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;oBACjC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;oBAEhF,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;wBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;wBAChE,IAAI,YAAY,EAAE,CAAC;4BACf,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACpC,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,YAAY,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;CACJ;AA5XD,wDA4XC"}