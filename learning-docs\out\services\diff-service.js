"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiffService = void 0;
const diff = __importStar(require("diff"));
/**
 * Implementation of the diff service.
 */
class DiffService {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    /**
     * Generate a unified diff between two versions of a file.
     *
     * @param previousContent The previous content of the file.
     * @param currentContent The current content of the file.
     * @param oldFilePath The path to use in the diff for the old file.
     * @param newFilePath The path to use in the diff for the new file.
     * @returns A promise that resolves to the unified diff.
     */
    async generateUnifiedDiff(previousContent, currentContent, oldFilePath, newFilePath) {
        try {
            // Use jsdiff to create a unified patch
            const unifiedDiff = diff.createPatch(newFilePath, previousContent || '', currentContent || '', `Previous: ${oldFilePath}`, `Current: ${newFilePath}`);
            this.loggerService.info(`Generated diff for: ${newFilePath}`);
            return unifiedDiff;
        }
        catch (error) {
            this.loggerService.error(`Failed to generate diff: ${error}`);
            throw error;
        }
    }
}
exports.DiffService = DiffService;
//# sourceMappingURL=diff-service.js.map