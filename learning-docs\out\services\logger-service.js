"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Implementation of the logger service.
 */
class LoggerService {
    outputChannel;
    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Learning Docs');
    }
    /**
     * Log an informational message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    info(message, ...args) {
        this.log('INFO', message, args);
    }
    /**
     * Log a warning message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    warn(message, ...args) {
        this.log('WARN', message, args);
    }
    /**
     * Log an error message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    error(message, ...args) {
        this.log('ERROR', message, args);
    }
    /**
     * Internal method to format and log a message.
     * @param level The log level.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    log(level, message, args) {
        const timestamp = new Date().toISOString();
        const formattedMessage = `[${timestamp}] [${level}] ${message}`;
        // Log to output channel
        this.outputChannel.appendLine(formattedMessage);
        // Also log to console for development
        if (args.length > 0) {
            console.log(formattedMessage, ...args);
        }
        else {
            console.log(formattedMessage);
        }
    }
    /**
     * Dispose of resources.
     */
    dispose() {
        this.outputChannel.dispose();
    }
}
exports.LoggerService = LoggerService;
//# sourceMappingURL=logger-service.js.map