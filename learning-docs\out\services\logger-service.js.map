{"version": 3, "file": "logger-service.js", "sourceRoot": "", "sources": ["../../src/services/logger-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC;;GAEG;AACH,MAAa,aAAa;IACd,aAAa,CAAuB;IAE5C;QACI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QACvC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,OAAe,EAAE,GAAG,IAAW;QACvC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAe,EAAE,GAAG,IAAW;QACxC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACK,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,IAAW;QACnD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,gBAAgB,GAAG,IAAI,SAAS,MAAM,KAAK,KAAK,OAAO,EAAE,CAAC;QAEhE,wBAAwB;QACxB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QAEhD,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AA7DD,sCA6DC"}