"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceInitializerService = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const crypto = __importStar(require("crypto"));
/**
 * Implementation of the workspace initializer service.
 */
class WorkspaceInitializerService {
    context;
    loggerService;
    workspaceService;
    storageService;
    fileHistoryService;
    diffService;
    INITIALIZATION_MARKER_FILE = '.learning-docs-initialized';
    constructor(context, loggerService, workspaceService, storageService, fileHistoryService, diffService) {
        this.context = context;
        this.loggerService = loggerService;
        this.workspaceService = workspaceService;
        this.storageService = storageService;
        this.fileHistoryService = fileHistoryService;
        this.diffService = diffService;
    }
    /**
     * Initialize the workspace on extension activation.
     */
    async initializeWorkspace() {
        this.loggerService.info('Starting workspace initialization');
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            this.loggerService.warn('No workspace found, skipping initialization');
            return;
        }
        const isFirstRun = await this.isFirstRunInWorkspace();
        if (isFirstRun) {
            this.loggerService.info('First run detected, performing initial workspace scan');
            await this.performInitialWorkspaceScan();
            await this.markWorkspaceAsInitialized();
        }
        else {
            this.loggerService.info('Resuming workspace tracking (existing data found)');
        }
        this.loggerService.info('Workspace initialization completed');
    }
    /**
     * Check if this is the first run in the current workspace.
     */
    async isFirstRunInWorkspace() {
        try {
            // Check for initialization marker in workspace state
            const isInitialized = this.context.workspaceState.get('learningDocs.initialized', false);
            if (isInitialized) {
                return false;
            }
            // Check for existing documentation records
            const hasRecords = await this.storageService.hasExistingRecords();
            if (hasRecords) {
                // Records exist but marker is missing, set the marker and consider it not first run
                await this.markWorkspaceAsInitialized();
                return false;
            }
            // Check for existing file history cache
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (workspaceRoot) {
                const cacheDir = path.join(this.context.globalStorageUri.fsPath, '.learningDocsCache', path.basename(workspaceRoot.fsPath));
                if (fs.existsSync(cacheDir)) {
                    const cacheFiles = await fs.promises.readdir(cacheDir);
                    if (cacheFiles.length > 0) {
                        // Cache exists but marker is missing, set the marker and consider it not first run
                        await this.markWorkspaceAsInitialized();
                        return false;
                    }
                }
            }
            return true;
        }
        catch (error) {
            this.loggerService.error(`Error checking first run status: ${error}`);
            // Default to first run to be safe
            return true;
        }
    }
    /**
     * Perform an initial workspace scan to baseline all existing files.
     */
    async performInitialWorkspaceScan() {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            this.loggerService.warn('No workspace root found for initial scan');
            return;
        }
        this.loggerService.info('Starting initial workspace scan');
        try {
            // Get ignored patterns for the file search
            const ignoredPatterns = this.getIgnoredPatterns();
            // Find all files in the workspace
            const files = await vscode.workspace.findFiles('**/*', `{${ignoredPatterns.join(',')}}`);
            this.loggerService.info(`Found ${files.length} files to baseline`);
            // Process files in batches to avoid blocking the UI
            const batchSize = 10;
            for (let i = 0; i < files.length; i += batchSize) {
                const batch = files.slice(i, i + batchSize);
                await this.processBatch(batch);
                // Yield to the event loop
                await new Promise(resolve => setImmediate(resolve));
            }
            this.loggerService.info(`Initial workspace scan completed: ${files.length} files baselined`);
        }
        catch (error) {
            this.loggerService.error(`Error during initial workspace scan: ${error}`);
            throw error;
        }
    }
    /**
     * Process a batch of files during the initial scan.
     */
    async processBatch(files) {
        for (const fileUri of files) {
            try {
                await this.baselineFile(fileUri);
            }
            catch (error) {
                this.loggerService.error(`Failed to baseline file ${fileUri.fsPath}: ${error}`);
                // Continue with other files
            }
        }
    }
    /**
     * Baseline a single file.
     */
    async baselineFile(fileUri) {
        const relativePath = this.workspaceService.getRelativePath(fileUri);
        if (!relativePath) {
            return;
        }
        // Read the file content
        const document = await vscode.workspace.openTextDocument(fileUri);
        const content = document.getText();
        // Store in file history cache as baseline
        await this.fileHistoryService.baselineFile(relativePath, content);
        // Create a baseline documentation record
        const record = {
            id: this.generateId(relativePath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'baseline',
            currentContent: content,
            previousContent: undefined,
            rawDiff: await this.diffService.generateUnifiedDiff('', content, relativePath, relativePath)
        };
        // Save the baseline record
        await this.storageService.saveDocumentationRecord(record);
        this.loggerService.info(`Baselined file: ${relativePath}`);
    }
    /**
     * Mark the workspace as successfully initialized.
     */
    async markWorkspaceAsInitialized() {
        try {
            await this.context.workspaceState.update('learningDocs.initialized', true);
            this.loggerService.info('Workspace marked as initialized');
        }
        catch (error) {
            this.loggerService.error(`Failed to mark workspace as initialized: ${error}`);
        }
    }
    /**
     * Get ignored patterns for file search.
     */
    getIgnoredPatterns() {
        // Use the same patterns as the workspace service
        return [
            '**/node_modules/**',
            '**/.git/**',
            '**/out/**',
            '**/dist/**',
            '**/.vscode/**',
            '**/*.log'
        ];
    }
    /**
     * Generate a unique ID for a baseline record.
     */
    generateId(filePath) {
        const timestamp = Date.now().toString();
        const hash = crypto.createHash('md5').update(`baseline:${filePath}:${timestamp}`).digest('hex');
        return hash;
    }
}
exports.WorkspaceInitializerService = WorkspaceInitializerService;
//# sourceMappingURL=workspace-initializer-service.js.map