{"version": 3, "file": "workspace-initializer-service.js", "sourceRoot": "", "sources": ["../../src/services/workspace-initializer-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AACzB,+CAAiC;AAUjC;;GAEG;AACH,MAAa,2BAA2B;IAIf;IACA;IACA;IACA;IACA;IACA;IARJ,0BAA0B,GAAG,4BAA4B,CAAC;IAE3E,YACqB,OAAgC,EAChC,aAA6B,EAC7B,gBAAmC,EACnC,cAA+B,EAC/B,kBAAuC,EACvC,WAAyB;QALzB,YAAO,GAAP,OAAO,CAAyB;QAChC,kBAAa,GAAb,aAAa,CAAgB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAAmB;QACnC,mBAAc,GAAd,cAAc,CAAiB;QAC/B,uBAAkB,GAAlB,kBAAkB,CAAqB;QACvC,gBAAW,GAAX,WAAW,CAAc;IAC3C,CAAC;IAEJ;;OAEG;IACI,KAAK,CAAC,mBAAmB;QAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAE7D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;QAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YACvE,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEtD,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACjF,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAC9B,IAAI,CAAC;YACD,qDAAqD;YACrD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAU,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAClG,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,2CAA2C;YAC3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAClE,IAAI,UAAU,EAAE,CAAC;gBACb,oFAAoF;gBACpF,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACxC,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,wCAAwC;YACxC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAC/D,IAAI,aAAa,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CACtB,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EACpC,oBAAoB,EACpB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CACtC,CAAC;gBAEF,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1B,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACvD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxB,mFAAmF;wBACnF,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;wBACxC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;YACtE,kCAAkC;YAClC,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,2BAA2B;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;QAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACpE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE3D,IAAI,CAAC;YACD,2CAA2C;YAC3C,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAElD,kCAAkC;YAClC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEzF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,MAAM,oBAAoB,CAAC,CAAC;YAEnE,oDAAoD;YACpD,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAE/B,0BAA0B;gBAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,KAAK,CAAC,MAAM,kBAAkB,CAAC,CAAC;QACjG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,KAAmB;QAC1C,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC;gBAChF,4BAA4B;YAChC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,OAAmB;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,wBAAwB;QACxB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEnC,0CAA0C;QAC1C,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAElE,yCAAyC;QACzC,MAAM,MAAM,GAA8B;YACtC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YACjC,QAAQ,EAAE,YAAY;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,UAAU;YACrB,cAAc,EAAE,OAAO;YACvB,eAAe,EAAE,SAAS;YAC1B,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC;SAC/F,CAAC;QAEF,2BAA2B;QAC3B,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAE1D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,YAAY,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B;QACnC,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;YAC3E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,4CAA4C,KAAK,EAAE,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACtB,iDAAiD;QACjD,OAAO;YACH,oBAAoB;YACpB,YAAY;YACZ,WAAW;YACX,YAAY;YACZ,eAAe;YACf,UAAU;SACb,CAAC;IACN,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,QAAgB;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,YAAY,QAAQ,IAAI,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAChG,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AA3MD,kEA2MC"}