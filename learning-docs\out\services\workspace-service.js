"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceService = void 0;
const vscode = __importStar(require("vscode"));
const minimatch_1 = require("minimatch");
/**
 * Implementation of the workspace service.
 */
class WorkspaceService {
    configService;
    loggerService;
    constructor(configService, loggerService) {
        this.configService = configService;
        this.loggerService = loggerService;
    }
    /**
     * Get the root URI of the workspace.
     * @returns The workspace root URI, or undefined if not in a workspace.
     */
    getWorkspaceRoot() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            this.loggerService.warn('No workspace folder found');
            return undefined;
        }
        // For simplicity, use the first workspace folder
        return workspaceFolders[0].uri;
    }
    /**
     * Get the path relative to the workspace root.
     * @param uri The URI to convert to a relative path.
     * @returns The relative path, or undefined if not in a workspace.
     */
    getRelativePath(uri) {
        const workspaceRoot = this.getWorkspaceRoot();
        if (!workspaceRoot) {
            return undefined;
        }
        return vscode.workspace.asRelativePath(uri, false);
    }
    /**
     * Check if a path should be ignored based on configuration.
     * @param filePath The path to check.
     * @returns True if the path should be ignored, false otherwise.
     */
    isPathIgnored(filePath) {
        const ignoredPatterns = this.configService.getSetting('ignoredFilesPatterns') || [
            '**/node_modules/**',
            '**/.git/**'
        ];
        return ignoredPatterns.some(pattern => (0, minimatch_1.minimatch)(filePath, pattern));
    }
}
exports.WorkspaceService = WorkspaceService;
//# sourceMappingURL=workspace-service.js.map