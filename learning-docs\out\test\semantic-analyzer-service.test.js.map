{"version": 3, "file": "semantic-analyzer-service.test.js", "sourceRoot": "", "sources": ["../../src/test/semantic-analyzer-service.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,qFAAgF;AAChF,+DAA2D;AAC3D,+DAA2D;AAC3D,qEAAiE;AAEjE,KAAK,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAC7C,IAAI,gBAAyC,CAAC;IAC9C,IAAI,aAA4B,CAAC;IACjC,IAAI,aAA4B,CAAC;IACjC,IAAI,gBAAkC,CAAC;IAEvC,oCAAoC;IACpC,MAAM,gBAAgB,GAAG,CACrB,IAAY,EACZ,IAAuB,EACvB,KAAmB,EACnB,MAAe,EACf,QAAkC,EACb,EAAE,CAAC,CAAC;QACzB,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,cAAc,EAAE,KAAK;QACrB,MAAM,EAAE,MAAM,IAAI,EAAE;QACpB,QAAQ,EAAE,QAAQ,IAAI,EAAE;KAC3B,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAEjD,UAAU,CAAC,GAAG,EAAE;QACZ,aAAa,GAAG,IAAI,8BAAa,EAAE,CAAC;QACpC,aAAa,GAAG,IAAI,8BAAa,CAAC,aAAa,CAAC,CAAC;QACjD,gBAAgB,GAAG,IAAI,oCAAgB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACtE,gBAAgB,GAAG,IAAI,mDAAuB,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACnG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;QACrE,2CAA2C;QAC3C,MAAM,kBAAkB,GAAG,aAAa,CAAC,UAAU,CAAC;QACpD,aAAa,CAAC,UAAU,GAAG,CAAI,GAAW,EAAiB,EAAE;YACzD,IAAI,GAAG,KAAK,yBAAyB,EAAE,CAAC;gBACpC,OAAO,KAAU,CAAC;YACtB,CAAC;YACD,OAAO,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAkB,CAAC;QACxE,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,SAAS,EACT,cAAc,EACd,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEjC,0BAA0B;QAC1B,aAAa,CAAC,UAAU,GAAG,kBAAkB,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,SAAS,EACT,cAAc,EACd,OAAO,EACP,sBAAsB,CACzB,CAAC;QAEF,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QAC5C,wDAAwD;QACxD,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC9D,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAK,OAAe,EAAE,GAAG,IAAW,EAAc,EAAE;YACtF,IAAI,OAAO,KAAK,sCAAsC,EAAE,CAAC;gBACrD,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;oBAClB,wDAAwD;oBACxD,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,+BAA+B,CAAC,CAAM,CAAC;gBAC3H,CAAC;gBACD,kCAAkC;gBAClC,OAAO,EAAO,CAAC;YACnB,CAAC;YACD,OAAO,sBAAsB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,kCAAkC;QAClC,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAE,OAAY,EAAE,EAAE;YACvD,OAAO;gBACH,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;aAChB,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,EAAE,EAAE,2BAA2B;QAC/B,4BAA4B,EAAE,kBAAkB;QAChD,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAEtE,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,sBAAsB,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QAC9C,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC9D,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAK,OAAe,EAAE,GAAG,IAAW,EAAc,EAAE;YACtF,IAAI,OAAO,KAAK,sCAAsC,EAAE,CAAC;gBACrD,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;oBAClB,4CAA4C;oBAC5C,OAAO,EAAO,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACJ,uDAAuD;oBACvD,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,kCAAkC,CAAC,CAAM,CAAC;gBACjI,CAAC;YACL,CAAC;YACD,OAAO,sBAAsB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAE,OAAY,EAAE,EAAE;YACvD,OAAO;gBACH,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;aAChB,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,+BAA+B,EAAE,mBAAmB;QACpD,EAAE,EAAE,0BAA0B;QAC9B,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC9D,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;QAEzE,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,sBAAsB,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC9D,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAK,OAAe,EAAE,GAAG,IAAW,EAAc,EAAE;YACtF,IAAI,OAAO,KAAK,sCAAsC,EAAE,CAAC;gBACrD,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;oBAClB,sCAAsC;oBACtC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,4CAA4C,CAAC,CAAM,CAAC;gBACxI,CAAC;qBAAM,CAAC;oBACJ,uCAAuC;oBACvC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,+BAA+B,CAAC,CAAM,CAAC;gBAC3H,CAAC;YACL,CAAC;YACD,OAAO,sBAAsB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAE,OAAY,EAAE,EAAE;YACvD,OAAO;gBACH,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;aAChB,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,4BAA4B,EAAE,mBAAmB;QACjD,yCAAyC,EAAE,kBAAkB;QAC7D,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC9D,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAEtE,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,sBAAsB,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QAE9D,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAK,OAAe,EAAE,GAAG,IAAW,EAAc,EAAE;YACtF,IAAI,OAAO,KAAK,sCAAsC,EAAE,CAAC;gBACrD,2BAA2B;gBAC3B,MAAM,YAAY,GAAG,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,oBAAoB,CAAC,CAAC;gBAC/G,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;gBACzH,OAAO,CAAC,WAAW,CAAM,CAAC;YAC9B,CAAC;YACD,OAAO,sBAAsB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAE,OAAY,EAAE,EAAE;YACvD,OAAO;gBACH,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;aAChB,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,EAAE,EAAE,2BAA2B;QAC/B,qCAAqC,EAAE,kBAAkB;QACzD,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAEnE,MAAM,cAAc,GAAG,MAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;QAC/E,MAAM,eAAe,GAAG,MAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC;QAEjF,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjD,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAClD,MAAM,CAAC,WAAW,CAAC,cAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,MAAM,CAAC,WAAW,CAAC,eAAgB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAE/D,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,sBAAsB,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;QAC7D,8CAA8C;QAC9C,MAAM,kBAAkB,GAAG,aAAa,CAAC,UAAU,CAAC;QACpD,aAAa,CAAC,UAAU,GAAG,CAAI,GAAW,EAAiB,EAAE;YACzD,IAAI,GAAG,KAAK,4CAA4C,EAAE,CAAC;gBACvD,OAAO,GAAQ,CAAC,CAAC,gBAAgB;YACrC,CAAC;YACD,OAAO,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAkB,CAAC;QACxE,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,cAAc,EAAE,mBAAmB;QACnC,cAAc,EAAE,8BAA8B;QAC9C,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEjC,0BAA0B;QAC1B,aAAa,CAAC,UAAU,GAAG,kBAAkB,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QAClD,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC9D,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAK,OAAe,EAAE,GAAG,IAAW,EAAc,EAAE;YACtF,IAAI,OAAO,KAAK,sCAAsC,EAAE,CAAC;gBACrD,SAAS,EAAE,CAAC;gBACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;oBAClB,kCAAkC;oBAClC,OAAO;wBACH,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC;wBACpE,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC;qBAClE,CAAC;gBACX,CAAC;qBAAM,CAAC;oBACJ,kCAAkC;oBAClC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAM,CAAC;gBACvF,CAAC;YACL,CAAC;YACD,OAAO,sBAAsB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAE,OAAY,EAAE,EAAE;YACvD,OAAO;gBACH,GAAG,EAAE,OAAO;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;aAChB,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACrD,yBAAyB,EACzB,iDAAiD,EACjD,OAAO,EACP,YAAY,CACf,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,cAAc,CAAC,MAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAClD,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,OAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,WAAW,CAAC,MAAO,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAEhD,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,sBAAsB,CAAC;QACxD,MAAM,CAAC,SAAS,CAAC,gBAAgB,GAAG,wBAAwB,CAAC;IACjE,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}