"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const workspace_initializer_service_1 = require("../services/workspace-initializer-service");
const logger_service_1 = require("../services/logger-service");
const workspace_service_1 = require("../services/workspace-service");
const config_service_1 = require("../services/config-service");
const storage_service_1 = require("../services/storage-service");
const file_history_service_1 = require("../services/file-history-service");
const diff_service_1 = require("../services/diff-service");
suite('WorkspaceInitializerService Test Suite', () => {
    let workspaceInitializer;
    let loggerService;
    let workspaceService;
    let storageService;
    let fileHistoryService;
    let diffService;
    let mockContext;
    suiteSetup(async () => {
        // Create a mock extension context
        mockContext = {
            workspaceState: {
                get: (key, defaultValue) => defaultValue,
                update: async (key, value) => { },
                keys: () => []
            },
            globalStorageUri: vscode.Uri.file(path.join(__dirname, '..', '..', 'test-storage')),
            subscriptions: []
        };
        // Initialize services
        loggerService = new logger_service_1.LoggerService();
        const configService = new config_service_1.ConfigService(loggerService);
        workspaceService = new workspace_service_1.WorkspaceService(configService, loggerService);
        storageService = new storage_service_1.StorageService(mockContext, loggerService, workspaceService);
        fileHistoryService = new file_history_service_1.FileHistoryService(mockContext, loggerService, workspaceService);
        diffService = new diff_service_1.DiffService(loggerService);
        workspaceInitializer = new workspace_initializer_service_1.WorkspaceInitializerService(mockContext, loggerService, workspaceService, storageService, fileHistoryService, diffService);
    });
    test('isFirstRunInWorkspace should return true for new workspace', async () => {
        // Clear any existing state
        await mockContext.workspaceState.update('learningDocs.initialized', undefined);
        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, true, 'Should be first run for new workspace');
    });
    test('isFirstRunInWorkspace should return false after initialization', async () => {
        // Mark as initialized
        await workspaceInitializer.markWorkspaceAsInitialized();
        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, false, 'Should not be first run after initialization');
    });
    test('markWorkspaceAsInitialized should set the marker', async () => {
        await workspaceInitializer.markWorkspaceAsInitialized();
        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, false, 'Workspace should be marked as initialized');
    });
    suiteTeardown(async () => {
        // Clean up test storage
        const testStorageDir = path.join(__dirname, '..', '..', 'test-storage');
        if (fs.existsSync(testStorageDir)) {
            fs.rmSync(testStorageDir, { recursive: true, force: true });
        }
    });
});
//# sourceMappingURL=workspace-initializer.test.js.map