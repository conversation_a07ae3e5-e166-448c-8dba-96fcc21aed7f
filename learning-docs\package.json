{"name": "learning-docs", "displayName": "learning-docs", "description": "A VSCode extension that detects code changes and provides AI-powered learning documentation", "version": "0.0.1", "publisher": "learning-docs-publisher", "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "learningDocs.showDocumentationPanel", "title": "Learning Docs: Show Documentation Panel"}, {"command": "learningDocs.showDocumentation", "title": "Learning Docs: Show Documentation"}, {"command": "learningDocs.refreshDocumentation", "title": "Learning Docs: Refresh Documentation", "icon": "$(refresh)"}], "views": {"explorer": [{"id": "learningDocsExplorer", "name": "Learning Documentation"}]}, "menus": {"view/title": [{"command": "learningDocs.refreshDocumentation", "when": "view == learningDocsExplorer", "group": "navigation"}], "editor/context": [{"command": "learningDocs.showDocumentation", "group": "navigation"}]}, "configuration": {"title": "Learning Docs", "properties": {"learningDocs.fastAgentUrl": {"type": "string", "default": "http://localhost:8000/analyze", "description": "URL for the FastAgent backend"}, "learningDocs.ignoredFilesPatterns": {"type": "array", "default": ["**/node_modules/**", "**/.git/**"], "description": "Patterns for files to ignore"}, "learningDocs.enable.semanticAnalysis": {"type": "boolean", "default": true, "description": "Enable semantic analysis of code changes"}, "learningDocs.semanticAnalysis.enabledLanguages": {"type": "array", "default": ["typescript", "javascript", "typescriptreact", "javascriptreact", "python", "java", "csharp", "go", "ruby", "php"], "description": "Language IDs for which semantic analysis is enabled"}, "learningDocs.semanticAnalysis.skipIfTextSimilarityAbove": {"type": "number", "default": 0.98, "minimum": 0, "maximum": 1, "description": "Skip semantic analysis if text similarity is above this threshold (0-1)"}, "learningDocs.semanticAnalysis.maxNestingDepth": {"type": "number", "default": 3, "minimum": 1, "maximum": 10, "description": "Maximum depth for analyzing nested symbols"}, "learningDocs.semanticAnalysis.enableRenameDetection": {"type": "boolean", "default": false, "description": "Enable heuristic detection of renamed elements"}, "learningDocs.enable.contextualAnalysis": {"type": "boolean", "default": true, "description": "Enable/disable contextual analysis of code changes"}, "learningDocs.enable.aiDocumentation": {"type": "boolean", "default": true, "description": "Enable/disable AI-powered documentation generation"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.100.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "eslint": "^9.25.1", "typescript": "^5.8.3"}, "dependencies": {"diff": "^8.0.2", "minimatch": "^10.0.1"}}