import * as vscode from 'vscode';
import { ChangeDescriptor } from './change';

/**
 * Interface for change event aggregator service.
 */
export interface IChangeEventAggregatorService {
    /**
     * Event that fires when a stable change is detected.
     */
    onDidStableChange: vscode.Event<ChangeDescriptor>;
    
    /**
     * Start listening for changes.
     */
    startListening(): void;
    
    /**
     * Stop listening for changes.
     */
    stopListening(): void;
}
