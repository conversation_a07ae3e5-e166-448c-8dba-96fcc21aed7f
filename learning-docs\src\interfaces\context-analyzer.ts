import { SemanticPrimitive } from './semantic-analyzer';

/**
 * Represents contextual information about a code change.
 */
export interface ContextualInfo {
    /**
     * Related files that might be affected by or related to this change.
     */
    relatedFiles?: string[];
    
    /**
     * References to the changed elements in other files.
     */
    references?: {
        filePath: string;
        lineNumber: number;
        context: string;
    }[];
    
    /**
     * Imports that are related to the changed elements.
     */
    relatedImports?: string[];
    
    /**
     * Dependencies that are related to the changed elements.
     */
    relatedDependencies?: string[];
    
    /**
     * Git commit history for the changed file.
     */
    commitHistory?: {
        commitHash: string;
        author: string;
        date: string;
        message: string;
    }[];
    
    /**
     * Test files that might be affected by this change.
     */
    relatedTests?: string[];
}

/**
 * Interface for context analyzer service.
 */
export interface IContextAnalyzerService {
    /**
     * Get contextual information for a change.
     * 
     * @param filePath The path to the file.
     * @param semanticPrimitives The semantic primitives extracted from the change.
     * @returns A promise that resolves to the contextual information, or null if analysis is not possible.
     */
    getContextualInfo(
        filePath: string,
        semanticPrimitives?: SemanticPrimitive[]
    ): Promise<ContextualInfo | null>;
}
