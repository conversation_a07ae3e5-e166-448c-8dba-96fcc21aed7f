/**
 * Interface for logging service.
 */
export interface ILoggerService {
    /**
     * Log an informational message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    info(message: string, ...args: any[]): void;
    
    /**
     * Log a warning message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    warn(message: string, ...args: any[]): void;
    
    /**
     * Log an error message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    error(message: string, ...args: any[]): void;
}
