import * as vscode from 'vscode';
import * as path from 'path';
import * as https from 'https';
import * as http from 'http';
import { IAIDocumentationService, AIAnalysis } from '../interfaces/ai-documentation';
import { ChangeDocumentationRecord } from '../interfaces/storage';
import { ILoggerService } from '../interfaces/logger';
import { IConfigService } from '../interfaces/config';

/**
 * Implementation of the AI documentation service.
 */
export class AIDocumentationService implements IAIDocumentationService {
    constructor(
        private readonly loggerService: ILoggerService,
        private readonly configService: IConfigService
    ) {}

    /**
     * Generate AI documentation for a change.
     *
     * @param record The change documentation record.
     * @returns A promise that resolves to the AI analysis, or null if generation is not possible.
     */
    public async generateDocumentation(
        record: ChangeDocumentationRecord
    ): Promise<AIAnalysis | null> {
        try {
            this.loggerService.info(`Generating AI documentation for: ${record.filePath}`);

            // Check if AI documentation is enabled
            if (!this.configService.isFeatureEnabled('aiDocumentation')) {
                this.loggerService.info('AI documentation is disabled');
                return null;
            }

            // Get the FastAgent URL from configuration
            const fastAgentUrl = this.configService.getSetting<string>('fastAgentUrl');
            if (!fastAgentUrl) {
                this.loggerService.warn('FastAgent URL is not configured');
                return null;
            }

            // Prepare the request payload
            const payload = {
                filePath: record.filePath,
                eventType: record.eventType,
                previousContent: record.previousContent || '',
                currentContent: record.currentContent || '',
                rawDiff: record.rawDiff || '',
                semanticPrimitives: record.semanticPrimitives || [],
                contextualInfo: record.contextualInfo || {}
            };

            // Send the request to the FastAgent
            const aiAnalysis = await this.sendRequestToFastAgent(fastAgentUrl, payload);

            if (aiAnalysis) {
                this.loggerService.info(`AI documentation generated for: ${record.filePath}`);
                return aiAnalysis;
            } else {
                this.loggerService.warn(`Failed to generate AI documentation for: ${record.filePath}`);
                return null;
            }
        } catch (error) {
            this.loggerService.error(`Error generating AI documentation: ${error}`);
            return null;
        }
    }

    /**
     * Send a request to the FastAgent.
     * @param url The URL of the FastAgent.
     * @param payload The payload to send.
     * @returns A promise that resolves to the AI analysis, or null if the request fails.
     */
    private async sendRequestToFastAgent(url: string, payload: any): Promise<AIAnalysis | null> {
        return new Promise((resolve, reject) => {
            // Determine if we're using http or https
            const client = url.startsWith('https') ? https : http;

            // Prepare the request options
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // Create the request
            const req = client.request(url, requestOptions, (res) => {
                let data = '';

                // Collect the response data
                res.on('data', (chunk) => {
                    data += chunk;
                });

                // Process the response when it's complete
                res.on('end', () => {
                    if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
                        try {
                            const aiAnalysis = JSON.parse(data) as AIAnalysis;
                            resolve(aiAnalysis);
                        } catch (error) {
                            this.loggerService.error(`Error parsing FastAgent response: ${error}`);
                            resolve(null);
                        }
                    } else {
                        this.loggerService.error(`FastAgent returned status code: ${res.statusCode}`);
                        resolve(null);
                    }
                });
            });

            // Handle request errors
            req.on('error', (error) => {
                this.loggerService.error(`Error sending request to FastAgent: ${error}`);
                resolve(null);
            });

            // Send the payload
            req.write(JSON.stringify(payload));
            req.end();
        });
    }

    /**
     * Generate a fallback AI analysis when the FastAgent is not available.
     * @param record The change documentation record.
     * @returns An AI analysis with basic information.
     */
    private generateFallbackAnalysis(record: ChangeDocumentationRecord): AIAnalysis {
        const fileExt = path.extname(record.filePath);
        const fileName = path.basename(record.filePath);

        let summary = '';
        let explanation = '';

        switch (record.eventType) {
            case 'create':
                summary = `Created a new ${fileExt} file: ${fileName}`;
                explanation = `This change introduces a new file named ${fileName} to the codebase.`;
                break;
            case 'modify':
                summary = `Modified ${fileName}`;
                explanation = `This change updates the existing file ${fileName}.`;
                break;
            case 'delete':
                summary = `Deleted ${fileName}`;
                explanation = `This change removes the file ${fileName} from the codebase.`;
                break;
        }

        // Add information about semantic primitives if available
        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            const addedPrimitives = record.semanticPrimitives.filter(p => p.operation === 'add');
            const removedPrimitives = record.semanticPrimitives.filter(p => p.operation === 'remove');
            const modifiedPrimitives = record.semanticPrimitives.filter(p => p.operation === 'modify');

            if (addedPrimitives.length > 0) {
                explanation += `\n\nAdded ${addedPrimitives.length} new elements:`;
                addedPrimitives.forEach(p => {
                    explanation += `\n- ${p.elementType} ${p.elementName}`;
                });
            }

            if (removedPrimitives.length > 0) {
                explanation += `\n\nRemoved ${removedPrimitives.length} elements:`;
                removedPrimitives.forEach(p => {
                    explanation += `\n- ${p.elementType} ${p.elementName}`;
                });
            }

            if (modifiedPrimitives.length > 0) {
                explanation += `\n\nModified ${modifiedPrimitives.length} elements:`;
                modifiedPrimitives.forEach(p => {
                    explanation += `\n- ${p.elementType} ${p.elementName}`;
                });
            }
        }

        return {
            summary,
            explanation,
            impact: 'Impact analysis requires the FastAgent to be available.',
            suggestions: [
                'Configure the FastAgent URL in the extension settings to get more detailed analysis.'
            ],
            learningResources: [
                {
                    title: 'VSCode Extension Documentation',
                    url: 'https://code.visualstudio.com/api',
                    description: 'Official documentation for VSCode extensions.'
                }
            ]
        };
    }
}
