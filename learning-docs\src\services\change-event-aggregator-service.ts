import * as vscode from 'vscode';
import * as path from 'path';
import * as crypto from 'crypto';
import { IChangeEventAggregatorService } from '../interfaces/change-event-aggregator';
import { ChangeDescriptor } from '../interfaces/change';
import { ILoggerService } from '../interfaces/logger';
import { IWorkspaceService } from '../interfaces/workspace';

/**
 * Implementation of the change event aggregator service.
 */
export class ChangeEventAggregatorService implements IChangeEventAggregatorService {
    private readonly _onDidStableChange = new vscode.EventEmitter<ChangeDescriptor>();
    public readonly onDidStableChange = this._onDidStableChange.event;
    
    private disposables: vscode.Disposable[] = [];
    private recentSaveEvents = new Map<string, number>(); // filePath -> timestamp
    private debounceTimeouts = new Map<string, NodeJS.Timeout>(); // filePath -> timeout
    
    constructor(
        private readonly loggerService: ILoggerService,
        private readonly workspaceService: IWorkspaceService
    ) {}
    
    /**
     * Start listening for changes.
     */
    public startListening(): void {
        this.loggerService.info('Starting to listen for file changes');
        
        // Listen for save events (primary for modifications)
        this.disposables.push(
            vscode.workspace.onDidSaveTextDocument(this.handleSaveEvent.bind(this))
        );
        
        // Create a file system watcher for all files
        const watcher = vscode.workspace.createFileSystemWatcher('**/*');
        
        // Handle file creation
        this.disposables.push(
            watcher.onDidCreate(this.handleCreateEvent.bind(this))
        );
        
        // Handle file deletion
        this.disposables.push(
            watcher.onDidDelete(this.handleDeleteEvent.bind(this))
        );
        
        // Handle external file changes
        this.disposables.push(
            watcher.onDidChange(this.handleExternalChangeEvent.bind(this))
        );
        
        // Add the watcher itself to disposables
        this.disposables.push(watcher);
    }
    
    /**
     * Stop listening for changes.
     */
    public stopListening(): void {
        this.loggerService.info('Stopping file change listeners');
        
        // Clear all debounce timeouts
        for (const timeout of this.debounceTimeouts.values()) {
            clearTimeout(timeout);
        }
        this.debounceTimeouts.clear();
        
        // Dispose all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
    
    /**
     * Handle a save event.
     * @param document The document that was saved.
     */
    private handleSaveEvent(document: vscode.TextDocument): void {
        const filePath = document.uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(document.uri);
        
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        
        this.loggerService.info(`File saved: ${relativePath}`);
        
        // Record this save event to prevent duplicate processing from external change events
        this.recentSaveEvents.set(filePath, Date.now());
        
        // Create a change descriptor
        const change: ChangeDescriptor = {
            id: this.generateId(filePath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'modify',
            currentContent: document.getText()
        };
        
        // Emit the change event
        this._onDidStableChange.fire(change);
    }
    
    /**
     * Handle a file creation event.
     * @param uri The URI of the created file.
     */
    private async handleCreateEvent(uri: vscode.Uri): Promise<void> {
        const filePath = uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(uri);
        
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        
        this.loggerService.info(`File created: ${relativePath}`);
        
        try {
            // Read the content of the new file
            const document = await vscode.workspace.openTextDocument(uri);
            const content = document.getText();
            
            // Create a change descriptor
            const change: ChangeDescriptor = {
                id: this.generateId(filePath),
                filePath: relativePath,
                timestamp: new Date().toISOString(),
                eventType: 'create',
                currentContent: content
            };
            
            // Emit the change event
            this._onDidStableChange.fire(change);
        } catch (error) {
            this.loggerService.error(`Failed to process file creation: ${error}`);
        }
    }
    
    /**
     * Handle a file deletion event.
     * @param uri The URI of the deleted file.
     */
    private handleDeleteEvent(uri: vscode.Uri): void {
        const relativePath = this.workspaceService.getRelativePath(uri);
        
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        
        this.loggerService.info(`File deleted: ${relativePath}`);
        
        // Create a change descriptor
        const change: ChangeDescriptor = {
            id: this.generateId(uri.fsPath),
            filePath: relativePath,
            timestamp: new Date().toISOString(),
            eventType: 'delete'
        };
        
        // Emit the change event
        this._onDidStableChange.fire(change);
    }
    
    /**
     * Handle an external file change event.
     * @param uri The URI of the changed file.
     */
    private handleExternalChangeEvent(uri: vscode.Uri): void {
        const filePath = uri.fsPath;
        const relativePath = this.workspaceService.getRelativePath(uri);
        
        if (!relativePath || this.workspaceService.isPathIgnored(relativePath)) {
            return;
        }
        
        // Check if we already processed a save event for this file recently
        const lastSaveTime = this.recentSaveEvents.get(filePath);
        if (lastSaveTime && Date.now() - lastSaveTime < 2000) {
            this.loggerService.info(`Ignoring external change for recently saved file: ${relativePath}`);
            return;
        }
        
        // Clear any existing debounce timeout for this file
        if (this.debounceTimeouts.has(filePath)) {
            clearTimeout(this.debounceTimeouts.get(filePath));
        }
        
        // Debounce the external change event (500ms)
        this.debounceTimeouts.set(filePath, setTimeout(async () => {
            this.loggerService.info(`External file change: ${relativePath}`);
            
            try {
                // Read the content of the changed file
                const document = await vscode.workspace.openTextDocument(uri);
                const content = document.getText();
                
                // Create a change descriptor
                const change: ChangeDescriptor = {
                    id: this.generateId(filePath),
                    filePath: relativePath,
                    timestamp: new Date().toISOString(),
                    eventType: 'modify',
                    currentContent: content
                };
                
                // Emit the change event
                this._onDidStableChange.fire(change);
            } catch (error) {
                this.loggerService.error(`Failed to process external file change: ${error}`);
            }
        }, 500));
    }
    
    /**
     * Generate a unique ID for a change.
     * @param filePath The path to the file.
     * @returns A unique ID.
     */
    private generateId(filePath: string): string {
        const timestamp = Date.now().toString();
        const hash = crypto.createHash('md5').update(`${filePath}:${timestamp}`).digest('hex');
        return hash;
    }
    
    /**
     * Dispose of resources.
     */
    public dispose(): void {
        this.stopListening();
        this._onDidStableChange.dispose();
    }
}
