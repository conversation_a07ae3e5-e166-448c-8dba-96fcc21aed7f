import * as vscode from 'vscode';
import { IConfigService } from '../interfaces/config';
import { ILoggerService } from '../interfaces/logger';

/**
 * Implementation of the configuration service.
 */
export class ConfigService implements IConfigService {
    private readonly configSection = 'learningDocs';

    constructor(private readonly loggerService: ILoggerService) {}

    /**
     * Get a configuration setting by key.
     * @param key The configuration key.
     * @returns The configuration value, or undefined if not found.
     */
    public getSetting<T>(key: string): T | undefined {
        this.loggerService.info(`[DEBUG_CONFIG] 🔍 Attempting to read config: Section='${this.configSection}', Key='${key}'`);

        const config = vscode.workspace.getConfiguration(this.configSection);

        // Debug: Check if the configuration section exists at all
        const allKeys = Object.keys(config);
        this.loggerService.info(`[DEBUG_CONFIG] 📋 All available keys in '${this.configSection}' section:`, allKeys);

        // Debug: Try to get the value with inspect() to see defaults vs user values
        const inspectResult = config.inspect<T>(key);
        this.loggerService.info(`[DEBUG_CONFIG] 🔍 Inspect result for '${key}':`, JSON.stringify(inspectResult, null, 2));

        const rawValue = config.get<T>(key);

        this.loggerService.info(`[DEBUG_CONFIG] 📄 Raw value from getConfiguration('${this.configSection}').get('${key}'):`, rawValue, `(Type: ${typeof rawValue})`);

        return rawValue;
    }

    /**
     * Check if a feature is enabled.
     * @param featureKey The feature key to check.
     * @returns True if the feature is enabled, false otherwise.
     */
    public isFeatureEnabled(featureKey: string): boolean {
        const fullKey = `enable.${featureKey}`;
        this.loggerService.info(`[DEBUG_CONFIG] 🎯 isFeatureEnabled called with featureKey='${featureKey}' -> fullKey='${fullKey}'`);

        const rawValue = this.getSetting<boolean>(fullKey);
        const result = rawValue ?? false;

        this.loggerService.info(`[DEBUG_CONFIG] ✅ isFeatureEnabled result for '${featureKey}': ${result} (rawValue was: ${rawValue})`);

        return result;
    }
}
