import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as child_process from 'child_process';
import { promisify } from 'util';
import { IContextAnalyzerService, ContextualInfo } from '../interfaces/context-analyzer';
import { SemanticPrimitive } from '../interfaces/semantic-analyzer';
import { ILoggerService } from '../interfaces/logger';
import { IConfigService } from '../interfaces/config';
import { IWorkspaceService } from '../interfaces/workspace';

const execFile = promisify(child_process.execFile);

/**
 * Implementation of the context analyzer service.
 */
export class ContextAnalyzerService implements IContextAnalyzerService {
    constructor(
        private readonly loggerService: ILoggerService,
        private readonly configService: IConfigService,
        private readonly workspaceService: IWorkspaceService
    ) {}

    /**
     * Get contextual information for a change.
     *
     * @param filePath The path to the file.
     * @param semanticPrimitives The semantic primitives extracted from the change.
     * @returns A promise that resolves to the contextual information, or null if analysis is not possible.
     */
    public async getContextualInfo(
        filePath: string,
        semanticPrimitives?: SemanticPrimitive[]
    ): Promise<ContextualInfo | null> {
        try {
            this.loggerService.info(`Getting contextual info for: ${filePath}`);

            // Initialize contextual info
            const contextualInfo: ContextualInfo = {};

            // Get related files
            contextualInfo.relatedFiles = await this.findRelatedFiles(filePath, semanticPrimitives);

            // Get references
            contextualInfo.references = await this.findReferences(filePath, semanticPrimitives);

            // Get related imports
            contextualInfo.relatedImports = await this.findRelatedImports(filePath);

            // Get related dependencies
            contextualInfo.relatedDependencies = await this.findRelatedDependencies(filePath);

            // Get commit history
            contextualInfo.commitHistory = await this.getCommitHistory(filePath);

            // Get related tests
            contextualInfo.relatedTests = await this.findRelatedTests(filePath);

            this.loggerService.info(`Contextual analysis completed for: ${filePath}`);
            return contextualInfo;
        } catch (error) {
            this.loggerService.error(`Error during contextual analysis: ${error}`);
            return null;
        }
    }

    /**
     * Find files that are related to the changed file.
     * @param filePath The path to the file.
     * @param semanticPrimitives The semantic primitives extracted from the change.
     * @returns A promise that resolves to an array of related file paths.
     */
    private async findRelatedFiles(
        filePath: string,
        semanticPrimitives?: SemanticPrimitive[]
    ): Promise<string[]> {
        const relatedFiles = new Set<string>();

        try {
            // If we have semantic primitives, use them to find related files
            if (semanticPrimitives && semanticPrimitives.length > 0) {
                // Extract names of changed elements
                const changedElementNames = semanticPrimitives.map(p => p.elementName);

                // Search for these names in other files
                for (const name of changedElementNames) {
                    // Skip very short names to avoid too many false positives
                    if (name.length < 3) {
                        continue;
                    }

                    // Use ripgrep or similar to search for the name in other files
                    const workspaceRoot = this.workspaceService.getWorkspaceRoot();
                    if (workspaceRoot) {
                        try {
                            const { stdout } = await execFile('rg', ['--files-with-matches', name], {
                                cwd: workspaceRoot.fsPath
                            });

                            const files = stdout.trim().split('\n').filter(f => f && f !== filePath);
                            files.forEach(f => relatedFiles.add(f));
                        } catch (error) {
                            // Ripgrep might not be installed, fallback to VSCode search
                            const results = await vscode.workspace.findFiles('**/*', '**/node_modules/**');

                            for (const uri of results) {
                                const relativePath = this.workspaceService.getRelativePath(uri);
                                if (relativePath && relativePath !== filePath) {
                                    try {
                                        const content = await fs.promises.readFile(uri.fsPath, 'utf-8');
                                        if (content.includes(name)) {
                                            relatedFiles.add(relativePath);
                                        }
                                    } catch (error) {
                                        // Ignore errors reading files
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Find files with similar names
            const fileBaseName = path.basename(filePath, path.extname(filePath));
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();

            if (workspaceRoot) {
                const results = await vscode.workspace.findFiles(`**/*${fileBaseName}*`, '**/node_modules/**');

                for (const uri of results) {
                    const relativePath = this.workspaceService.getRelativePath(uri);
                    if (relativePath && relativePath !== filePath) {
                        relatedFiles.add(relativePath);
                    }
                }
            }

            return Array.from(relatedFiles);
        } catch (error) {
            this.loggerService.error(`Error finding related files: ${error}`);
            return [];
        }
    }

    /**
     * Find references to the changed elements in other files.
     * @param filePath The path to the file.
     * @param semanticPrimitives The semantic primitives extracted from the change.
     * @returns A promise that resolves to an array of references.
     */
    private async findReferences(
        filePath: string,
        semanticPrimitives?: SemanticPrimitive[]
    ): Promise<ContextualInfo['references']> {
        const references: ContextualInfo['references'] = [];

        try {
            // If we have semantic primitives, use them to find references
            if (semanticPrimitives && semanticPrimitives.length > 0) {
                // Extract names of changed elements
                const changedElementNames = semanticPrimitives.map(p => p.elementName);

                // Search for these names in other files
                for (const name of changedElementNames) {
                    // Skip very short names to avoid too many false positives
                    if (name.length < 3) {
                        continue;
                    }

                    // Use ripgrep or similar to search for the name in other files
                    const workspaceRoot = this.workspaceService.getWorkspaceRoot();
                    if (workspaceRoot) {
                        try {
                            const { stdout } = await execFile('rg', ['-n', name], {
                                cwd: workspaceRoot.fsPath
                            });

                            const lines = stdout.trim().split('\n');
                            for (const line of lines) {
                                const match = line.match(/^(.+):(\d+):(.*)$/);
                                if (match) {
                                    const [, matchFilePath, lineNumber, context] = match;

                                    // Skip the file itself
                                    if (matchFilePath === filePath) {
                                        continue;
                                    }

                                    references.push({
                                        filePath: matchFilePath,
                                        lineNumber: parseInt(lineNumber, 10),
                                        context: context.trim()
                                    });
                                }
                            }
                        } catch (error) {
                            // Ripgrep might not be installed, fallback to VSCode search
                            // This is a simplified fallback and won't be as accurate
                        }
                    }
                }
            }

            return references;
        } catch (error) {
            this.loggerService.error(`Error finding references: ${error}`);
            return [];
        }
    }

    /**
     * Find imports that are related to the changed file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to an array of related imports.
     */
    private async findRelatedImports(filePath: string): Promise<string[]> {
        const relatedImports: string[] = [];

        try {
            // Read the file content
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (!workspaceRoot) {
                return [];
            }

            const fullPath = path.join(workspaceRoot.fsPath, filePath);
            const content = await fs.promises.readFile(fullPath, 'utf-8');

            // Extract imports based on file extension
            const fileExt = path.extname(filePath);

            if (fileExt === '.js' || fileExt === '.ts' || fileExt === '.jsx' || fileExt === '.tsx') {
                // JavaScript/TypeScript import patterns
                const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+[^;]*|[^;]*)\s+from\s+['"]([^'"]+)['"]/g;
                let match;

                while ((match = importRegex.exec(content)) !== null) {
                    relatedImports.push(match[1]);
                }

                // Also check for require statements
                const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;

                while ((match = requireRegex.exec(content)) !== null) {
                    relatedImports.push(match[1]);
                }
            } else if (fileExt === '.py') {
                // Python import patterns
                const importRegex = /(?:from\s+([^\s]+)\s+import|import\s+([^\s]+))/g;
                let match;

                while ((match = importRegex.exec(content)) !== null) {
                    relatedImports.push(match[1] || match[2]);
                }
            }
            // Add more language-specific import patterns as needed

            return relatedImports;
        } catch (error) {
            this.loggerService.error(`Error finding related imports: ${error}`);
            return [];
        }
    }

    /**
     * Find dependencies that are related to the changed file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to an array of related dependencies.
     */
    private async findRelatedDependencies(filePath: string): Promise<string[]> {
        const relatedDependencies: string[] = [];

        try {
            // Check for package.json
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (!workspaceRoot) {
                return [];
            }

            const packageJsonPath = path.join(workspaceRoot.fsPath, 'package.json');

            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf-8'));

                // Get all dependencies
                const allDependencies = {
                    ...(packageJson.dependencies || {}),
                    ...(packageJson.devDependencies || {})
                };

                // Read the file content
                const fullPath = path.join(workspaceRoot.fsPath, filePath);
                const content = await fs.promises.readFile(fullPath, 'utf-8');

                // Check which dependencies are used in the file
                for (const dep in allDependencies) {
                    if (content.includes(dep)) {
                        relatedDependencies.push(`${dep}@${allDependencies[dep]}`);
                    }
                }
            }

            return relatedDependencies;
        } catch (error) {
            this.loggerService.error(`Error finding related dependencies: ${error}`);
            return [];
        }
    }

    /**
     * Get the commit history for a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to an array of commit history entries.
     */
    private async getCommitHistory(filePath: string): Promise<ContextualInfo['commitHistory']> {
        const commitHistory: ContextualInfo['commitHistory'] = [];

        try {
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (!workspaceRoot) {
                return [];
            }

            // Use git to get commit history
            try {
                const { stdout } = await execFile(
                    'git',
                    ['log', '--pretty=format:%H|%an|%ad|%s', '--date=iso', '--', filePath],
                    { cwd: workspaceRoot.fsPath }
                );

                const lines = stdout.trim().split('\n');
                for (const line of lines) {
                    const [commitHash, author, date, message] = line.split('|');

                    commitHistory.push({
                        commitHash,
                        author,
                        date,
                        message
                    });
                }
            } catch (error) {
                // Git might not be available or the file might not be in a git repository
                this.loggerService.warn(`Could not get git history: ${error}`);
            }

            return commitHistory;
        } catch (error) {
            this.loggerService.error(`Error getting commit history: ${error}`);
            return [];
        }
    }

    /**
     * Find test files that are related to the changed file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to an array of related test file paths.
     */
    private async findRelatedTests(filePath: string): Promise<string[]> {
        const relatedTests: string[] = [];

        try {
            const fileBaseName = path.basename(filePath, path.extname(filePath));
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();

            if (workspaceRoot) {
                // Look for test files with similar names
                const testPatterns = [
                    `**/${fileBaseName}.test.*`,
                    `**/${fileBaseName}.spec.*`,
                    `**/test_${fileBaseName}.*`,
                    `**/tests/${fileBaseName}.*`,
                    `**/test/${fileBaseName}.*`,
                    `**/__tests__/${fileBaseName}.*`
                ];

                for (const pattern of testPatterns) {
                    const results = await vscode.workspace.findFiles(pattern, '**/node_modules/**');

                    for (const uri of results) {
                        const relativePath = this.workspaceService.getRelativePath(uri);
                        if (relativePath) {
                            relatedTests.push(relativePath);
                        }
                    }
                }
            }

            return relatedTests;
        } catch (error) {
            this.loggerService.error(`Error finding related tests: ${error}`);
            return [];
        }
    }
}
