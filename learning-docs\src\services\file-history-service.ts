import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import * as child_process from 'child_process';
import { promisify } from 'util';
import { IFileHistoryService } from '../interfaces/file-history';
import { ILoggerService } from '../interfaces/logger';
import { IWorkspaceService } from '../interfaces/workspace';

const execFile = promisify(child_process.execFile);

/**
 * Implementation of the file history service.
 */
export class FileHistoryService implements IFileHistoryService {
    private inMemoryCache = new Map<string, string>();
    private cacheDir: string;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly loggerService: ILoggerService,
        private readonly workspaceService: IWorkspaceService
    ) {
        // Create cache directory within the extension's global storage
        this.cacheDir = path.join(context.globalStorageUri.fsPath, '.learningDocsCache');
        this.ensureCacheDirectoryExists();
    }

    /**
     * Get the previous version of a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the previous content, or undefined if not found.
     */
    public async getPreviousVersion(filePath: string): Promise<string | undefined> {
        this.loggerService.info(`Getting previous version for: ${filePath}`);

        // Try in-memory cache first (fastest)
        if (this.inMemoryCache.has(filePath)) {
            this.loggerService.info(`Found previous version in memory cache for: ${filePath}`);
            return this.inMemoryCache.get(filePath);
        }

        // Try Git next
        try {
            const gitContent = await this.getFromGit(filePath);
            if (gitContent) {
                this.loggerService.info(`Found previous version in Git for: ${filePath}`);
                return gitContent;
            }
        } catch (error) {
            this.loggerService.warn(`Failed to get previous version from Git: ${error}`);
        }

        // Try persistent cache
        try {
            const cacheContent = await this.getFromPersistentCache(filePath);
            if (cacheContent) {
                this.loggerService.info(`Found previous version in persistent cache for: ${filePath}`);
                return cacheContent;
            }
        } catch (error) {
            this.loggerService.warn(`Failed to get previous version from persistent cache: ${error}`);
        }

        this.loggerService.warn(`No previous version found for: ${filePath}`);
        return undefined;
    }

    /**
     * Update the cache with the current content of a file.
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    public async updateCache(filePath: string, content: string): Promise<void> {
        // Update in-memory cache
        this.inMemoryCache.set(filePath, content);

        // Update persistent cache
        try {
            await this.updatePersistentCache(filePath, content);
        } catch (error) {
            this.loggerService.error(`Failed to update persistent cache: ${error}`);
        }
    }

    /**
     * Baseline a file during initial workspace scan.
     * This stores the current content as the initial "previous version" for the file.
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    public async baselineFile(filePath: string, content: string): Promise<void> {
        this.loggerService.info(`Baselining file: ${filePath}`);

        // Store in both caches, same as updateCache
        await this.updateCache(filePath, content);
    }

    /**
     * Get the previous version of a file from Git.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the content from Git, or undefined if not found.
     */
    private async getFromGit(filePath: string): Promise<string | undefined> {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return undefined;
        }

        try {
            // Check if the file is in a Git repository
            const { stdout: gitRootOutput } = await execFile('git', ['rev-parse', '--show-toplevel'], {
                cwd: workspaceRoot.fsPath
            });

            if (!gitRootOutput.trim()) {
                return undefined;
            }

            // Get the relative path from the Git root
            const gitRoot = gitRootOutput.trim();
            const relativePath = path.relative(gitRoot, path.join(workspaceRoot.fsPath, filePath));

            // Get the content from the HEAD commit
            const { stdout: gitContent } = await execFile('git', ['show', `HEAD:${relativePath}`], {
                cwd: workspaceRoot.fsPath
            });

            return gitContent;
        } catch (error) {
            // File might not be in Git yet, or other Git error
            return undefined;
        }
    }

    /**
     * Get the previous version of a file from the persistent cache.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the content from the cache, or undefined if not found.
     */
    private async getFromPersistentCache(filePath: string): Promise<string | undefined> {
        const workspaceId = this.getWorkspaceId();
        const fileHash = this.hashFilePath(filePath);
        const cachePath = path.join(this.cacheDir, workspaceId, fileHash);

        if (!fs.existsSync(cachePath)) {
            return undefined;
        }

        // Get all cache files for this file path
        const cacheFiles = await fs.promises.readdir(cachePath);
        if (cacheFiles.length === 0) {
            return undefined;
        }

        // Sort by timestamp (newest first)
        cacheFiles.sort((a, b) => {
            const timestampA = parseInt(a.split('_')[0], 10);
            const timestampB = parseInt(b.split('_')[0], 10);
            return timestampB - timestampA;
        });

        // Get the most recent cache file
        const mostRecentFile = cacheFiles[0];
        const content = await fs.promises.readFile(path.join(cachePath, mostRecentFile), 'utf-8');

        return content;
    }

    /**
     * Update the persistent cache with the current content of a file.
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    private async updatePersistentCache(filePath: string, content: string): Promise<void> {
        const workspaceId = this.getWorkspaceId();
        const fileHash = this.hashFilePath(filePath);
        const cachePath = path.join(this.cacheDir, workspaceId, fileHash);

        // Ensure the cache directory exists
        if (!fs.existsSync(cachePath)) {
            fs.mkdirSync(cachePath, { recursive: true });
        }

        // Create a cache file with timestamp
        const timestamp = Date.now();
        const cacheFile = path.join(cachePath, `${timestamp}_${path.basename(filePath)}`);

        await fs.promises.writeFile(cacheFile, content);

        // Prune old cache files (keep only the 5 most recent)
        await this.pruneCache(cachePath);
    }

    /**
     * Prune old cache files, keeping only the most recent ones.
     * @param cachePath The path to the cache directory.
     */
    private async pruneCache(cachePath: string): Promise<void> {
        const maxCacheFiles = 5;

        const cacheFiles = await fs.promises.readdir(cachePath);
        if (cacheFiles.length <= maxCacheFiles) {
            return;
        }

        // Sort by timestamp (newest first)
        cacheFiles.sort((a, b) => {
            const timestampA = parseInt(a.split('_')[0], 10);
            const timestampB = parseInt(b.split('_')[0], 10);
            return timestampB - timestampA;
        });

        // Delete older files
        for (let i = maxCacheFiles; i < cacheFiles.length; i++) {
            await fs.promises.unlink(path.join(cachePath, cacheFiles[i]));
        }
    }

    /**
     * Ensure the cache directory exists.
     */
    private ensureCacheDirectoryExists(): void {
        if (!fs.existsSync(this.cacheDir)) {
            fs.mkdirSync(this.cacheDir, { recursive: true });
        }
    }

    /**
     * Get a unique identifier for the current workspace.
     * @returns A string identifier for the workspace.
     */
    private getWorkspaceId(): string {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return 'no-workspace';
        }

        // Use the last segment of the workspace path as an identifier
        return path.basename(workspaceRoot.fsPath);
    }

    /**
     * Hash a file path to create a safe directory name.
     * @param filePath The file path to hash.
     * @returns A hash of the file path.
     */
    private hashFilePath(filePath: string): string {
        return crypto.createHash('md5').update(filePath).digest('hex');
    }
}
