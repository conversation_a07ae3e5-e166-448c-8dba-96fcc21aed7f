import * as vscode from 'vscode';
import { ILoggerService } from '../interfaces/logger';

/**
 * Implementation of the logger service.
 */
export class LoggerService implements ILoggerService {
    private outputChannel: vscode.OutputChannel;
    
    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Learning Docs');
    }
    
    /**
     * Log an informational message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    public info(message: string, ...args: any[]): void {
        this.log('INFO', message, args);
    }
    
    /**
     * Log a warning message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    public warn(message: string, ...args: any[]): void {
        this.log('WARN', message, args);
    }
    
    /**
     * Log an error message.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    public error(message: string, ...args: any[]): void {
        this.log('ERROR', message, args);
    }
    
    /**
     * Internal method to format and log a message.
     * @param level The log level.
     * @param message The message to log.
     * @param args Additional arguments to include in the log.
     */
    private log(level: string, message: string, args: any[]): void {
        const timestamp = new Date().toISOString();
        const formattedMessage = `[${timestamp}] [${level}] ${message}`;
        
        // Log to output channel
        this.outputChannel.appendLine(formattedMessage);
        
        // Also log to console for development
        if (args.length > 0) {
            console.log(formattedMessage, ...args);
        } else {
            console.log(formattedMessage);
        }
    }
    
    /**
     * Dispose of resources.
     */
    public dispose(): void {
        this.outputChannel.dispose();
    }
}
