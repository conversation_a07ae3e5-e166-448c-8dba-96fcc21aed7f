import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { WorkspaceInitializerService } from '../services/workspace-initializer-service';
import { LoggerService } from '../services/logger-service';
import { WorkspaceService } from '../services/workspace-service';
import { ConfigService } from '../services/config-service';
import { StorageService } from '../services/storage-service';
import { FileHistoryService } from '../services/file-history-service';
import { DiffService } from '../services/diff-service';

suite('WorkspaceInitializerService Test Suite', () => {
    let workspaceInitializer: WorkspaceInitializerService;
    let loggerService: LoggerService;
    let workspaceService: WorkspaceService;
    let storageService: StorageService;
    let fileHistoryService: FileHistoryService;
    let diffService: DiffService;
    let mockContext: vscode.ExtensionContext;

    suiteSetup(async () => {
        // Create a mock extension context
        mockContext = {
            workspaceState: {
                get: (key: string, defaultValue?: any) => defaultValue,
                update: async (key: string, value: any) => {},
                keys: () => []
            },
            globalStorageUri: vscode.Uri.file(path.join(__dirname, '..', '..', 'test-storage')),
            subscriptions: []
        } as any;

        // Initialize services
        loggerService = new LoggerService();
        const configService = new ConfigService(loggerService);
        workspaceService = new WorkspaceService(configService, loggerService);
        storageService = new StorageService(mockContext, loggerService, workspaceService);
        fileHistoryService = new FileHistoryService(mockContext, loggerService, workspaceService);
        diffService = new DiffService(loggerService);

        workspaceInitializer = new WorkspaceInitializerService(
            mockContext,
            loggerService,
            workspaceService,
            storageService,
            fileHistoryService,
            diffService
        );
    });

    test('isFirstRunInWorkspace should return true for new workspace', async () => {
        // Clear any existing state
        await mockContext.workspaceState.update('learningDocs.initialized', undefined);

        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, true, 'Should be first run for new workspace');
    });

    test('isFirstRunInWorkspace should return false after initialization', async () => {
        // Mark as initialized
        await workspaceInitializer.markWorkspaceAsInitialized();

        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, false, 'Should not be first run after initialization');
    });

    test('markWorkspaceAsInitialized should set the marker', async () => {
        await workspaceInitializer.markWorkspaceAsInitialized();

        const isFirstRun = await workspaceInitializer.isFirstRunInWorkspace();
        assert.strictEqual(isFirstRun, false, 'Workspace should be marked as initialized');
    });

    suiteTeardown(async () => {
        // Clean up test storage
        const testStorageDir = path.join(__dirname, '..', '..', 'test-storage');
        if (fs.existsSync(testStorageDir)) {
            fs.rmSync(testStorageDir, { recursive: true, force: true });
        }
    });
});
